<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Menu UI">
      <UniqueIdentifier>{b9f15ea9-f7cd-4d83-8fe7-0a5e88fb2a94}</UniqueIdentifier>
    </Filter>
    <Filter Include="Menu UI\Framwork">
      <UniqueIdentifier>{e91f96fe-3c0e-478b-ace7-db36dbd3e4c8}</UniqueIdentifier>
    </Filter>
    <Filter Include="Menu UI\Fonts">
      <UniqueIdentifier>{2b5a6d72-f4b1-4df2-8dcd-394e69e3ff1f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Menu UI\Components">
      <UniqueIdentifier>{4a30d29d-3cae-4adf-9ead-e44d7388c3c2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Menu UI\ImGui">
      <UniqueIdentifier>{d741c8db-8e03-4b12-aae7-b9fdb8c7ad0d}</UniqueIdentifier>
    </Filter>
    <Filter Include="Render">
      <UniqueIdentifier>{10e50b13-6b6d-4acb-a367-3a88ee81923c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Render\Cheat Menu">
      <UniqueIdentifier>{d3e4ac55-0c41-465f-9c7a-33b9e7dd1b64}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheat Core">
      <UniqueIdentifier>{8e06f19f-64d2-44cb-9fb4-60f61e52ced6}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheat Core\Framwork">
      <UniqueIdentifier>{36a3c9a5-de09-4f1a-be1b-3e4f45c81f7e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheat Core\Kernel Driver">
      <UniqueIdentifier>{a89f1a97-32e9-4ca7-bf19-c8bf20545ca3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheat Core\Kernel Driver\Include">
      <UniqueIdentifier>{4b135263-6e92-4130-b0a2-6e8ae8bc471e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheat Core\Kernel Driver\Include\PortableExecutable">
      <UniqueIdentifier>{ab60d6f0-b26f-4f5b-8b87-a259328e227f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheat Core\Kernel Driver\Driver">
      <UniqueIdentifier>{81ac3431-1a41-4ded-8017-ae2783b481fc}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheat Core\GameClass">
      <UniqueIdentifier>{c5a3eb2f-9d65-4ad8-82ed-93f2b3055c21}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheat Core\Features">
      <UniqueIdentifier>{00d95aa1-899b-42bb-b98e-994fb2913a36}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheat Core\Features\Aimbot">
      <UniqueIdentifier>{993728db-d52c-483b-970e-3c399f4ed1b4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheat Core\Features\Visuals">
      <UniqueIdentifier>{6b3dbb2b-6f4d-4e54-8d41-9eba6545324c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheat Core\Features\Caching">
      <UniqueIdentifier>{96906c3a-3049-43b3-b585-532a57b94991}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheat Core\Features\Loot">
      <UniqueIdentifier>{94be44ac-d245-465d-bf86-b6362cba4107}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheat Core\Features\Config">
      <UniqueIdentifier>{8b2e12d9-ac62-4198-9cd0-e1f465225943}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheat Core\Features\Visuals\Drawing">
      <UniqueIdentifier>{bac7f3fd-97a4-4f4c-a626-f495fd95130e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheat Core\Features\Settings">
      <UniqueIdentifier>{e62fd924-e8ef-4610-b70f-683ceeb3df68}</UniqueIdentifier>
    </Filter>
    <Filter Include="Menu UI\Components\Animation">
      <UniqueIdentifier>{ea68bc10-d431-48ff-a0f3-bf1ef7936fa6}</UniqueIdentifier>
    </Filter>
    <Filter Include="Menu UI\Components\Animation\ImAnim">
      <UniqueIdentifier>{b6e186fc-1456-4321-b95b-824c49ad614c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Menu UI\ImGui\blur">
      <UniqueIdentifier>{578dc435-e55a-487f-9b4a-45a05ce7f54e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheat Core\Features\Input">
      <UniqueIdentifier>{5a70eaa7-5608-4796-8639-59bf11d09d50}</UniqueIdentifier>
    </Filter>
    <Filter Include="Menu UI\Blur">
      <UniqueIdentifier>{b569d94c-fb5b-467d-b5d1-a759a17a52f4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cheat Core\Features\FontSystem">
      <UniqueIdentifier>{6a27789c-1050-4e75-8dcc-bfeaf58b9edc}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <!-- Menu UI files -->
    <ClCompile Include="Menu UI\Components\Components.cpp">
      <Filter>Menu UI\Components</Filter>
    </ClCompile>
    <ClCompile Include="Menu UI\Framwork\GUI.cpp">
      <Filter>Menu UI\Framwork</Filter>
    </ClCompile>
    <!-- Render files -->
    <ClCompile Include="Render\Cheat Menu\CheatMenu.cpp">
      <Filter>Render\Cheat Menu</Filter>
    </ClCompile>
    <!-- Cheat Core files -->
    <!-- Kernel Driver files -->
    <!-- Main file -->
    <ClCompile Include="main.cpp" />
    <ClCompile Include="Cheat Core\Kernel Driver\Include\PortableExecutable\PortableExecutable.cpp">
      <Filter>Cheat Core\Kernel Driver\Include\PortableExecutable</Filter>
    </ClCompile>
    <ClCompile Include="Cheat Core\Features\Aimbot\Aimbot.cpp">
      <Filter>Cheat Core\Features\Aimbot</Filter>
    </ClCompile>
    <ClCompile Include="Cheat Core\Features\Caching\Cache.cpp">
      <Filter>Cheat Core\Features\Caching</Filter>
    </ClCompile>
    <ClCompile Include="Cheat Core\Features\Loot\Loot.cpp">
      <Filter>Cheat Core\Features\Loot</Filter>
    </ClCompile>
    <ClCompile Include="Cheat Core\Features\Visuals\PlayerVisuals.cpp">
      <Filter>Cheat Core\Features\Visuals</Filter>
    </ClCompile>
    <ClCompile Include="Cheat Core\Features\Config\Config.cpp">
      <Filter>Cheat Core\Features\Config</Filter>
    </ClCompile>
    <ClCompile Include="Cheat Core\Features\Visuals\Drawing\Drawing.cpp">
      <Filter>Cheat Core\Features\Visuals\Drawing</Filter>
    </ClCompile>
    <ClCompile Include="Cheat Core\Features\Loot\icons.cpp">
      <Filter>Cheat Core\Features\Loot</Filter>
    </ClCompile>
    <ClCompile Include="Cheat Core\Settings\Settings.cpp">
      <Filter>Cheat Core\Features\Settings</Filter>
    </ClCompile>
    <ClCompile Include="Menu UI\Components\nav_elements.cpp">
      <Filter>Menu UI\Components</Filter>
    </ClCompile>
    <ClCompile Include="Menu UI\Components\Animation\ImAnim\AbstractAnimation.cpp">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClCompile>
    <ClCompile Include="Menu UI\Components\Animation\ImAnim\AnimationGroup.cpp">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClCompile>
    <ClCompile Include="Menu UI\Components\Animation\ImAnim\Easing.cpp">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClCompile>
    <ClCompile Include="Menu UI\Components\Animation\ImAnim\EasingCurve.cpp">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClCompile>
    <ClCompile Include="Menu UI\Components\Animation\ImAnim\ImVec2Anim.cpp">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClCompile>
    <ClCompile Include="Menu UI\Components\Animation\ImAnim\ImVec4Anim.cpp">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClCompile>
    <ClCompile Include="Menu UI\Components\Animation\ImAnim\ParallelAnimationGroup.cpp">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClCompile>
    <ClCompile Include="Menu UI\Components\Animation\ImAnim\PauseAnimation.cpp">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClCompile>
    <ClCompile Include="Menu UI\Components\Animation\ImAnim\SequentialAnimationGroup.cpp">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClCompile>
    <ClCompile Include="Menu UI\Components\Animation\ImAnim\Utils.cpp">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClCompile>
    <ClCompile Include="Menu UI\ImGui\imgui.cpp">
      <Filter>Menu UI\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="Menu UI\ImGui\imgui_demo.cpp">
      <Filter>Menu UI\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="Menu UI\ImGui\imgui_draw.cpp">
      <Filter>Menu UI\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="Menu UI\ImGui\imgui_impl_dx11.cpp">
      <Filter>Menu UI\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="Menu UI\ImGui\imgui_impl_win32.cpp">
      <Filter>Menu UI\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="Menu UI\ImGui\imgui_tables.cpp">
      <Filter>Menu UI\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="Menu UI\ImGui\imgui_widgets.cpp">
      <Filter>Menu UI\ImGui</Filter>
    </ClCompile>
    <ClCompile Include="Utils\SignatureScanner.cpp" />
    <ClCompile Include="Menu UI\Framwork\AnimationSystem.cpp">
      <Filter>Menu UI\Framwork</Filter>
    </ClCompile>
    <ClCompile Include="Menu UI\ImGui\stb_image_impl.cpp" />
    <ClCompile Include="Menu UI\HImGuiImageManager\HImGuiImageManager.cpp" />
    <ClCompile Include="Cheat Core\Features\FontSystem\FontSystem.cpp">
      <Filter>Cheat Core\Features\FontSystem</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <!-- Menu UI files -->
    <ClInclude Include="Menu UI\Components\Components.h">
      <Filter>Menu UI\Components</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\Framwork\GUI.h">
      <Filter>Menu UI\Framwork</Filter>
    </ClInclude>
    <!-- Render files -->
    <ClInclude Include="Render\Cheat Menu\CheatMenu.h">
      <Filter>Render\Cheat Menu</Filter>
    </ClInclude>
    <!-- Cheat Core files -->
    <!-- Kernel Driver files -->
    <ClInclude Include="Cheat Core\Kernel Driver\Include\PortableExecutable\PortableExecutable.h">
      <Filter>Cheat Core\Kernel Driver\Include\PortableExecutable</Filter>
    </ClInclude>
    <ClInclude Include="Cheat Core\Kernel Driver\Include\utils.h">
      <Filter>Cheat Core\Kernel Driver\Include</Filter>
    </ClInclude>
    <ClInclude Include="Cheat Core\Kernel Driver\Include\Variadicstring.h">
      <Filter>Cheat Core\Kernel Driver\Include</Filter>
    </ClInclude>
    <ClInclude Include="Cheat Core\Kernel Driver\Driver\Driver.h">
      <Filter>Cheat Core\Kernel Driver\Driver</Filter>
    </ClInclude>
    <ClInclude Include="Cheat Core\Features\Aimbot\Aimbot.h">
      <Filter>Cheat Core\Features\Aimbot</Filter>
    </ClInclude>
    <ClInclude Include="Cheat Core\Features\Caching\Cache.h">
      <Filter>Cheat Core\Features\Caching</Filter>
    </ClInclude>
    <ClInclude Include="Cheat Core\Framwork\Math.h">
      <Filter>Cheat Core\Framwork</Filter>
    </ClInclude>
    <ClInclude Include="Cheat Core\Framwork\Vectors.h">
      <Filter>Cheat Core\Framwork</Filter>
    </ClInclude>
    <ClInclude Include="Cheat Core\GameClass\GameFunctions.h">
      <Filter>Cheat Core\GameClass</Filter>
    </ClInclude>
    <ClInclude Include="Cheat Core\GameClass\Offsets.h">
      <Filter>Cheat Core\GameClass</Filter>
    </ClInclude>
    <ClInclude Include="Cheat Core\Features\Loot\Loot.h">
      <Filter>Cheat Core\Features\Loot</Filter>
    </ClInclude>
    <ClInclude Include="Cheat Core\Features\Visuals\PlayerVisuals.h">
      <Filter>Cheat Core\Features\Visuals</Filter>
    </ClInclude>
    <ClInclude Include="Cheat Core\Framwork\HexArray.h">
      <Filter>Cheat Core\Framwork</Filter>
    </ClInclude>
    <ClInclude Include="Cheat Core\Features\Config\Config.h">
      <Filter>Cheat Core\Features\Config</Filter>
    </ClInclude>
    <ClInclude Include="Cheat Core\GameClass\GameSettings.h">
      <Filter>Cheat Core\GameClass</Filter>
    </ClInclude>
    <ClInclude Include="Cheat Core\Features\Visuals\Drawing\Drawing.h">
      <Filter>Cheat Core\Features\Visuals\Drawing</Filter>
    </ClInclude>
    <ClInclude Include="Cheat Core\Features\Loot\icons.h">
      <Filter>Cheat Core\Features\Loot</Filter>
    </ClInclude>
    <ClInclude Include="Utils.h">
      <Filter>Cheat Core\Framwork</Filter>
    </ClInclude>
    <ClInclude Include="Cheat Core\Settings\Settings.h">
      <Filter>Cheat Core\Features\Settings</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\Components\nav_elements.h">
      <Filter>Menu UI\Components</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\AbstractAnimation.h">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\AnimationGroup.h">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\custom_functions.h">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\Easing.h">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\EasingCurve.h">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\ImVec2Anim.h">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\ImVec4Anim.h">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\ParallelAnimationGroup.h">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\PauseAnimation.h">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\SequentialAnimationGroup.h">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\Utils.h">
      <Filter>Menu UI\Components\Animation\ImAnim</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\Fonts\icons.h">
      <Filter>Menu UI\Fonts</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\ImGui\blur\blur.hpp">
      <Filter>Menu UI\ImGui\blur</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\ImGui\imconfig.h">
      <Filter>Menu UI\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\ImGui\imgui.h">
      <Filter>Menu UI\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\ImGui\imgui_impl_dx11.h">
      <Filter>Menu UI\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\ImGui\imgui_impl_win32.h">
      <Filter>Menu UI\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\ImGui\imgui_internal.h">
      <Filter>Menu UI\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\ImGui\imgui_settings.h">
      <Filter>Menu UI\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\ImGui\imstb_rectpack.h">
      <Filter>Menu UI\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\ImGui\imstb_textedit.h">
      <Filter>Menu UI\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\ImGui\imstb_truetype.h">
      <Filter>Menu UI\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\ImGui\stb_image.h">
      <Filter>Menu UI\ImGui</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\Fonts\font_defines.h">
      <Filter>Menu UI\Fonts</Filter>
    </ClInclude>
    <ClInclude Include="Utils\SignatureScanner.h" />
    <ClInclude Include="Cheat Core\Features\Input\HotkeySystem.h">
      <Filter>Cheat Core\Features\Input</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\Framwork\AnimationSystem.h">
      <Filter>Menu UI\Framwork</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\Framwork\GUI_Helper.h">
      <Filter>Menu UI\Framwork</Filter>
    </ClInclude>
    <ClInclude Include="Menu UI\HImGuiImageManager\Logger.h" />
    <ClInclude Include="Menu UI\HImGuiImageManager\HImGuiImageManager.h" />
    <ClInclude Include="Cheat Core\Features\FontSystem\FontSystem.h">
      <Filter>Cheat Core\Features\FontSystem</Filter>
    </ClInclude>
    <!-- SDK files -->
  </ItemGroup>
</Project>